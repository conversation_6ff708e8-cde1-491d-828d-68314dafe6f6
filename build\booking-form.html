<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Massage Price Calculator</title>

    <!-- External CSS and Font Resources -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css?family=Lato:300,400|Poppins:300,400,800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@200..800&display=swap" rel="stylesheet">



    <!-- Custom Styles -->
    <style>
        /*===========================================*/
        /*              GLOBAL STYLES                */
        /*===========================================*/

        /* Global Box Model */
        *, *::before, *::after {
            box-sizing: border-box;
        }

        /* Global interactive element styles */
        button, [role="button"], .clickable, a {
            -webkit-tap-highlight-color: transparent;
        }

        /* Accessibility Focus Styles */
        button:focus, [role="button"]:focus, .clickable:focus, a:focus,
        .service-option:focus, .duration-option:focus {
            outline: 2px solid hsl(184, 70%, 35%);
            outline-offset: 2px;
            box-shadow: 0 0 0 4px rgba(79, 209, 197, 0.3);
        }

        /* Focus styles for keyboard navigation only */
        button:focus:not(:focus-visible),
        [role="button"]:focus:not(:focus-visible),
        .clickable:focus:not(:focus-visible),
        a:focus:not(:focus-visible),
        .service-option:focus:not(:focus-visible),
        .duration-option:focus:not(:focus-visible) {
            outline: none;
            box-shadow: none;
        }

        button:focus-visible,
        [role="button"]:focus-visible,
        .clickable:focus-visible,
        a:focus-visible,
        .service-option:focus-visible,
        .duration-option:focus-visible {
            outline: 2px solid hsl(184, 70%, 35%);
            outline-offset: 2px;
            box-shadow: 0 0 0 4px rgba(79, 209, 197, 0.3);
        }

        /*===========================================*/
        /*              UTILITY CLASSES              */
        /*===========================================*/

        /* Typography Utilities */
        .text-center { text-align: center; }
        .text-sm { font-size: 0.875rem; line-height: 1.25rem; }
        .text-lg { font-size: 1.125rem; line-height: 1.75rem; }
        .text-xl { font-size: 1.25rem; line-height: 1.75rem; }
        .text-2xl { font-size: 1.5rem; line-height: 2rem; }
        .font-medium { font-weight: 500; }
        .font-semibold { font-weight: 600; }
        .font-bold { font-weight: 700; }
        .opacity-90 { opacity: 0.9; }
        .text-white { color: white; }

        /* Layout Utilities */
        .flex { display: flex; }
        .grid { display: grid; }
        .flex-wrap { flex-wrap: wrap; }
        .justify-between { justify-content: space-between; }
        .justify-center { justify-content: center; }
        .items-center { align-items: center; }
        .items-start { align-items: flex-start; }
        .grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
        .gap-2 { gap: 0.5rem; }
        .gap-3 { gap: 0.75rem; }

        /* Container Utilities */
        .w-full { width: 100%; }
        .hidden { display: none; }

        /* Spacing Utilities - Margins */
        .mt-1 { margin-top: 0.25rem; }
        .mt-3 { margin-top: 0.75rem; }
        .mb-2 { margin-bottom: 0.5rem; }
        .mb-3 { margin-bottom: 0.75rem; }
        .mb-4 { margin-bottom: 1rem; }
        .mb-6 { margin-bottom: 1.5rem; }
        .mr-1 { margin-right: 0.25rem; }
        .ml-1 { margin-left: 0.25rem; }

        /* Spacing Utilities - Paddings */
        .p-3 { padding: 0.75rem; }
        .p-4 { padding: 1rem; }
        .px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
        .px-4 { padding-left: 1rem; padding-right: 1rem; }
        .px-8 { padding-left: 2rem; padding-right: 2rem; }
        .py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
        .py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
        .py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }

        /* Border Utilities */
        .rounded-md { border-radius: 0.375rem; }
        .rounded-lg { border-radius: 0.5rem; }
        .rounded-xl { border-radius: 0.75rem; }
        .rounded-2xl { border-radius: 1rem; }
        .rounded-3xl { border-radius: 1.5rem; }
        .rounded-4xl { border-radius: 2rem; }
        .rounded-full { border-radius: 9999px; }

        /* Effect Utilities */
        .shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
        .shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1); }
        .shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1); }

        /* Transition Utilities */
        .transition { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
        .transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
        .duration-300 { transition-duration: 300ms; }

        /* Button State Utilities */
        .disabled\\:opacity-50:disabled { opacity: 0.5; }
        .disabled\\:cursor-not-allowed:disabled { cursor: not-allowed; }

        /*===========================================*/
        /*           RESPONSIVE UTILITIES           */
        /*===========================================*/

        /* Breakpoint Variables (for reference in comments) */
        /* --mobile-max: 640px; */
        /* --tablet-min: 641px; */
        /* --tablet-max: 1024px; */
        /* --desktop-min: 1025px; */

        /* Mobile First Responsive Utilities */
        .grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
        .flex-col { flex-direction: column; }

        /* Tablet (641px and up) */
        @media (min-width: 641px) {
            .md\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
            .md\\:flex-row { flex-direction: row; }
            .md\\:text-left { text-align: left; }
        }

        /* Desktop (1025px and up) */
        @media (min-width: 1025px) {
            .lg\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
        }

        /*===========================================*/
        /*           SERVICE OPTION STYLES           */
        /*===========================================*/

        /* Service Options Container */
        #service-options {
            width: 100%;
            box-sizing: border-box;
        }

        /* Duration Options Container */
        #duration-options {
            width: 100%;
            box-sizing: border-box;
        }

        /* Service Text Styles */
        .service-selector-h3 {
            color: #555;
            font-family: 'Poppins', sans-serif;
            font-size: 1.125rem;
            margin-bottom: 0.15rem; /* Reduced from 0.25rem to move description up */
            line-height: 1.3; /* Slightly reduced line height */
        }

        .service-selector-p {
            color: #666;
            font-family: 'Lato', sans-serif;
            line-height: 1.3; /* Reduced from 1.4 to make text more compact */
            margin-top: -2px; /* Negative margin to pull text up closer to the title */
        }

        .service-text-container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding-top: 2px; /* Slight padding to better align with icon */
        }

        /* Mobile Service Text Styles will be consolidated in the MOBILE RESPONSIVE STYLES section */

        /*===========================================*/
        /*               BODY STYLES                */
        /*===========================================*/

        body {
            font-family: 'Lato', sans-serif;
            background: linear-gradient(to top left, #4fd1c5, #e6fffa);
            background-size: 400% 400%;
            animation: gradientBG 15s ease infinite;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 1rem;
            margin: 0;
        }

        @keyframes gradientBG {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /*===========================================*/
        /*           CALCULATOR CARD STYLES          */
        /*===========================================*/

        /* Main Card Container */
        .calculator-card {
            background-color: #eee;
            border-radius: 1rem;
            box-shadow: 0 35px 50px -12px rgba(0, 0, 0, 0.25);
            max-width: 600px;
            width: 100%;
            position: relative;
            transition: transform 0.05s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.3s ease;
            border: 1px solid #aaa;
            overflow: hidden;
            box-sizing: border-box; /* Ensure padding is included in width calculation */
        }

        .calculator-card:hover {
            box-shadow: 0 0 25px 8px rgba(255, 255, 255, 0.7);
        }

        /* Card Header */
        .card-header {
            width: 100%;
            background-color: hsl(184, 70%, 40%);
            color: white;
            padding: 1.5rem;
            text-align: center;
            border-top-left-radius: 1rem;
            border-top-right-radius: 1rem;
            position: relative;
            z-index: 2;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            box-sizing: border-box; /* Ensure padding is included in width calculation */
        }

        /* Card Body */
        .card-body {
            padding: 1.5rem;
            background-color: white;
            position: relative;
            z-index: 2;
            box-sizing: border-box; /* Ensure padding is included in width calculation */
        }

        /*===========================================*/
        /*          SERVICE OPTION STYLES            */
        /*===========================================*/

        /* Service Option Container */
        .service-option {
            cursor: pointer;
            transition: all 0.2s ease;
            border: 2px solid transparent;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            padding: 16px 20px; /* Reduced vertical padding to make layout more compact */
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
            width: 100%; /* Ensure full width in grid cell */
            box-sizing: border-box; /* Ensure padding is included in width calculation */
            -webkit-tap-highlight-color: transparent; /* Prevent blue flash on mobile tap */
            outline: none; /* Remove outline on click/focus */
            user-select: none; /* Prevent text selection */
            display: none;
            align-items: center;
            justify-content: space-between;
        }

        /* Service Option Animation */
        #service-section:not(.hidden-section) .service-option,
        #person-section:not(.hidden-section) .service-option {
            display: flex;
            opacity: 0;
            transform: translateY(10px);
            animation: fadeInUp 0.5s cubic-bezier(0.25, 0.1, 0.25, 1.0) forwards;
            animation-delay: calc(var(--animation-order, 0) * 0.08s + 0.1s); /* Faster staggered effect with a base delay */
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Service Option States */
        .service-option:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
            border-color: rgba(79, 209, 197, 0.3);
            background-color: rgba(255, 255, 255, 0.9);
            transition: all 0.2s ease;
        }

        .service-option.selected {
            color: white;
            border-color: hsl(184, 70%, 40%);
            background-color: hsl(184, 70%, 40%);
            transform: translateY(-2px);
        }

        /* Service Icon Styles */
        .service-selector-icon {
            font-size: 1.8rem; /* Much larger icon */
            color: hsl(184, 70%, 35%);
            margin-right: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 60px; /* Fixed width for alignment */
            height: 60px; /* Fixed height for alignment */
            flex-shrink: 0; /* Prevent icon from shrinking */
            background-color: rgba(79, 209, 197, 0.3);
            border-radius: 100%;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
            border: 2px solid transparent;
            opacity: 0;
            animation: icon-intro 1.5s forwards alternate;
            transition: all 0.2s ease;
        }

        /* Icon Animations */
        @keyframes icon-intro {
            0% {
                opacity: 0;
                transform: scale(0.5);
            }
            50% {
                transform: scale(1.1);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes icon-bounce {
            0% {
                transform: scale(0.5);
            }
            50% {
                transform: scale(1.1);
            }
            100% {
                transform: scale(1);
            }
        }

        @keyframes icon-hover {
            0% {
                transform: scale(0.8);
            }
            100% {
                transform: scale(1.1);
            }
        }

        /* Icon State Styles */
        .service-option:hover .service-selector-icon {
            opacity: 1;
            animation: icon-hover 0.5s forwards alternate !important;
        }

        .service-option.selected .service-selector-icon {
            opacity: 1;
            animation: icon-bounce 0.5s forwards alternate !important;
            box-shadow: inset 0 0 6px 2px rgba(79, 209, 197, 0.5);
            color: #ffffff;
            transition: color 0.2s ease;
        }

        /* Selected Service Text Styles */
        .service-option.selected .service-selector-h3 {
            text-shadow: 0 0 10px rgba(79, 209, 197, 0.5);
            color: #ffffff;
            transition: color 0.2s ease;
        }

        .service-option.selected .service-selector-p {
            text-shadow: 0 0 10px rgba(79, 209, 197, 0.5);
            color: #EEEEEE;
            transition: color 0.2s ease;
        }

        /*===========================================*/
        /*         DURATION OPTION STYLES           */
        /*===========================================*/

        /* Duration Option Container */
        .duration-option {
            cursor: pointer;
            transition: all 0.2s ease;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            padding: 10px 20px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(79, 209, 197, 0.2);
            display: inline-block; /* Ensure proper inline layout */
            margin-right: 0.5rem; /* Add spacing between options */
            margin-bottom: 0.5rem; /* Add spacing for wrapping */
            box-sizing: border-box; /* Ensure padding is included in width calculation */
            -webkit-tap-highlight-color: transparent; /* Prevent blue flash on mobile tap */
            outline: none; /* Remove outline on click/focus */
            user-select: none; /* Prevent text selection */
        }

        /* Duration Option States */
        .duration-option:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
            border-color: rgba(79, 209, 197, 0.5);
        }

        .duration-option.selected {
            transform: translateY(-2px);
            background-color: hsl(184, 70%, 40%);
            color: white;
        }

        /* Duration Text Styles */
        .duration-selector-h3 {
            color: #555;
        }

        .duration-selector-p {
            color: hsl(184, 70%, 35%);
        }

        /* Selected Duration Text Styles */
        .duration-option.selected .duration-selector-h3,
        .duration-option.selected .duration-selector-p {
            color: white;
        }

        /*===========================================*/
        /*         RIVE ANIMATION CONTAINER          */
        /*===========================================*/

        /* Main Animation Container */
        #animation-container {
            width: 100%;
            min-height: 240px;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            margin-bottom: 1rem;
            background-color: #f8fafc;
            border-radius: 1rem;
            padding: 1rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(79, 209, 197, 0.2);
            gap: 2.5rem;
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.25, 0.1, 0.25, 1.0);
            box-sizing: border-box; /* Ensure padding is included in width calculation */
        }

        /* Canvas Element */
        #canvas {
            width: 160px;
            height: 160px;
            flex-shrink: 0;
        }

        /* Rive Canvas Container */
        .rive-canvas-container {
            position: absolute;
            width: 160px;
            height: 160px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 999px;
            box-shadow: 0 0px 4px 2px rgb(255, 255, 255);
            transition: all 0.0s cubic-bezier(0.25, 0.1, 0.25, 1.0); /* Improved easing for smoother motion */
        }

        /* Centered Canvas Container */
        .rive-canvas-container.centered {
            position: absolute;
            left: 50%;
            transform: translateX(-50%); /* Centered position */
        }

        /* Intro Animation Active State */
        .intro-animation-active {
            min-height: 200px !important;
            justify-content: center !important;
            align-items: center !important;
        }

        /* Relaxation Level Text Container */
        .current-relaxation-level {
            transition: opacity 0.7s cubic-bezier(0.25, 0.1, 0.25, 1.0), transform 0.7s cubic-bezier(0.25, 0.1, 0.25, 1.0);
        }

        /* Hidden Text State */
        .current-relaxation-level.hidden-text {
            opacity: 0;
            transform: translateX(30px);
            pointer-events: none;
            display: none; /* Completely hide it initially */
        }

        /*===========================================*/
        /*         MOBILE RESPONSIVE STYLES          */
        /*===========================================*/

        /* Mobile Styles (max-width: 640px) */
        @media (max-width: 640px) {
            /* ---- Animation and Relaxation Level ---- */

            /* Mobile Animation Container */
            #animation-container {
                flex-direction: column !important; /* Force column layout on mobile */
                padding: 1rem;
                margin-bottom: 1rem;
                gap: 0rem; /* Reset gap for mobile layout */
                min-height: 320px; /* Ensure enough space for both elements */
            }

            /* Mobile Canvas Element */
            #canvas {
                margin-bottom: 0.5rem;
            }

            /* Mobile Relaxation Level Text */
            .current-relaxation-level {
                text-align: center;
                padding: 0.75rem;
                align-items: center;
                justify-content: center; /* Center content vertically */
                max-width: 100%;
                width: 100%; /* Full width on mobile */
                min-width: unset; /* Override desktop min-width */
                margin-top: 0.5rem; /* Add space between canvas and text */
            }

            /* Mobile Relaxation Title */
            .relaxation-title {
                text-align: center;
                font-size: 1.35rem;
                margin-bottom: 0.75rem;
                width: 100%; /* Ensure full width for proper centering */
            }

            /* Mobile Relaxation Value */
            .relaxation-value {
                text-align: center;
                font-size: 2rem;
                width: 100%; /* Ensure full width for proper centering */
            }

            /* Mobile Canvas Container Positioning */
            #animation-container > div:first-child {
                margin: 0 auto;
                display: flex;
                justify-content: center;
                position: relative !important; /* Force relative positioning */
                left: auto !important; /* Reset any absolute positioning */
                transform: none !important; /* Reset any transforms */
            }

            /* Mobile Centered Canvas Container */
            .rive-canvas-container.centered {
                position: relative !important;
                left: auto !important;
                transform: none !important;
                margin: 0 auto;
            }

            /* Mobile Canvas Container */
            .rive-canvas-container {
                margin: 0 auto;
                position: relative !important;
                width: 160px !important;
                height: 160px !important;
            }

            /* Mobile Hidden Text State */
            .current-relaxation-level.hidden-text {
                opacity: 0;
                transform: none;
                display: none; /* Completely hide it on mobile */
            }

            /* Mobile Intro Animation Active State */
            .intro-animation-active {
                min-height: 200px !important;
                flex-direction: column !important;
            }

            /* ---- Service Options ---- */

            /* Mobile Service Text Styles */
            .service-selector-h3 {
                font-size: 1rem;
                margin-bottom: 0.1rem; /* Reduced from 0.1rem to move description up */
                line-height: 1.15; /* Slightly reduced line height */
            }

            .service-selector-p {
                line-height: 1.15; /* Reduced from 1.2 to make text more compact */
                font-size: 0.8rem;
                margin-top: -0.5px; /* Negative margin to pull text up closer to the title */
            }

            .service-text-container {
                min-height: 40px; /* Match the icon height */
            }

            /* Mobile Grid Layout */
            .grid-cols-1 {
                grid-template-columns: repeat(1, minmax(0, 1fr));
            }

            /* Mobile Service Options */
            .service-option {
                padding: 12px; /* Reduced padding */
                margin-bottom: 8px; /* Reduced margin between options */
            }

            /* Mobile Service Icons */
            .service-selector-icon {
                font-size: 1.125rem; /* Smaller icon */
                width: 40px; /* Smaller width */
                height: 40px; /* Smaller height */
                margin-right: 0.5rem; /* Less spacing */
                display: flex;
                align-items: center;
                justify-content: center;
                align-self: center; /* Ensure vertical centering */
            }

            /* Mobile Service Options Container */
            #service-options {
                gap: 0.5rem; /* Smaller gap */
            }

            /* Mobile Service Section */
            #service-section {
                margin-bottom: 1rem; /* Reduced from 1.5rem */
            }

            /* ---- Touch Target Optimization ---- */

            /* Ensure all interactive elements have adequate touch targets */
            button,
            [role="button"],
            .clickable,
            .service-option,
            .duration-option,
            a {
                min-height: 44px; /* Minimum height for touch targets */
                min-width: 44px; /* Minimum width for touch targets */
            }

            /* Add extra padding to small buttons for better touch targets */
            .edit-button {
                padding: 8px 12px;
            }

            /* ---- Book Now and Ready Message Styles ---- */

            .ready-message-item {
                display: block;
                margin-bottom: 0.5rem;
                font-size: 1.1rem;
                text-align: center;
                padding: 0.4rem 0;
                border-bottom: 1px solid rgba(79, 209, 197, 0.15);
            }

            .ready-message-item:last-child {
                margin-bottom: 0;
                border-bottom: none;
            }

            /* No need to hide separator in mobile view anymore since we're using stacked format */

            .ready-message-title {
                margin-bottom: 1rem !important;
                font-size: 1.2rem !important;
                text-align: center;
                border-bottom: 1px solid rgba(79, 209, 197, 0.3);
                padding-bottom: 0.75rem;
            }

            .ready-message-details {
                padding: 0 0.5rem;
            }

            #book-now-container .mb-3.text-xl {
                padding: 1.25rem !important;
                margin-bottom: 1.75rem !important;
            }

            /* Book Now Button - Larger touch target on mobile */
            .book-now-button {
                padding: 1rem 2rem;
                width: 100%;
                max-width: 300px;
                margin: 0 auto;
            }
        }
        /*===========================================*/
        /*       RELAXATION LEVEL INDICATOR          */
        /*===========================================*/

        /* Relaxation Level Container */
        .current-relaxation-level {
            text-align: left;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            padding: 1.25rem;
            border-radius: 1rem;
            height: 100%;
            max-width: 200px;
            min-width: 250px;
        }

        /* Relaxation Title */
        .relaxation-title {
            margin-bottom: 0.75rem;
            font-size: 1.5rem;
            font-weight: 600;
            color: hsl(184, 70%, 35%);
            font-family: 'Poppins', sans-serif;
            line-height: 1.2;
        }

        /* Relaxation Value */
        .relaxation-value {
            font-size: 2.25rem;
            font-weight: 700;
            color: #555;
            font-family: 'Lato', sans-serif;
            line-height: 1.2;
        }

        /*===========================================*/
        /*           TRANSITION EFFECTS              */
        /*===========================================*/

        /* Global Transition Effects */
        .service-option, .duration-option, #person-section, #service-section, #duration-section,
        #person-options, #service-options, #duration-options,
        #person-summary, #service-summary, #duration-summary,
        #book-now-container {
            transition: all 0.35s cubic-bezier(0.25, 0.1, 0.25, 1.0);
            /* Slightly longer duration with improved easing for smoother transitions */
        }

        /*===========================================*/
        /*           BOOK NOW BUTTON STYLES           */
        /*===========================================*/

        /* Book Now Container */
        #book-now-container {
            margin-top: 2rem;
            opacity: 0;
            transform: translateY(20px) scale(0.95);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out, background-color 0.6s ease-out;
            padding: 1.5rem;
            border-radius: 1rem;
        }

        /* Visible Book Now Container */
        #book-now-container.visible {
            opacity: 1;
            transform: translateY(0) scale(1);
            background: linear-gradient(to bottom, rgba(79, 209, 197, 0.15), rgba(79, 209, 197, 0.05));
            box-shadow: 0 10px 25px -5px rgba(79, 209, 197, 0.25), inset 0 1px 2px rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(79, 209, 197, 0.3);
        }

        /* Book Now Button */
        .book-now-button {
            position: relative;
            overflow: hidden;
            transform: scale(1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            box-shadow: 0 10px 25px -5px rgba(79, 209, 197, 0.5);
            border-radius: 1rem;
            padding: 0.75rem;
            width: 96%;
            padding-left: 3.5rem;
            padding-right: 3.5rem;
        }

        /* Button Hover State */
        .book-now-button:hover {
            transform: scale(1.05);
            box-shadow: 0 15px 30px -5px rgba(79, 209, 197, 0.7);
            animation: pulse 2s infinite;
        }

        /* Button Active State */
        .book-now-button:active {
            transform: scale(0.98);
        }

        /* Button Shine Effect */
        .book-now-button::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(
                to bottom right,
                rgba(255, 255, 255, 0) 0%,
                rgba(255, 255, 255, 0.1) 50%,
                rgba(255, 255, 255, 0) 100%
            );
            transform: rotate(45deg);
            animation: shine 6s infinite;
        }

        /* Ready Message Styles */
        .ready-message-item {
            display: block;
            color: hsl(184, 70%, 35%);
            font-weight: 700;
            margin-bottom: 0.5rem;
            padding: 0.4rem 0;
            border-bottom: 1px solid rgba(79, 209, 197, 0.15);
        }

        .ready-message-item:last-child {
            margin-bottom: 0;
            border-bottom: none;
        }

        .ready-message-title {
            border-bottom: 1px solid rgba(79, 209, 197, 0.3);
            padding-bottom: 0.75rem;
        }

        .ready-message-details {
            text-align: center;
            padding: 0 0.5rem;
        }

        /* Button Animations */
        @keyframes highlight-text {
            0% { text-shadow: 0 1px 1px rgba(255,255,255,0.8); }
            50% { text-shadow: 0 0 8px rgba(79, 209, 197, 0.5); }
            100% { text-shadow: 0 1px 1px rgba(255,255,255,0.8); }
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(79, 209, 197, 0.7);
            }
            70% {
                box-shadow: 0 0 0 14px rgba(79, 209, 197, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(79, 209, 197, 0);
            }
        }

        @keyframes shine {
            0% {
                left: -200%;
                top: -100%;
            }
            100% {
                left: 100%;
                top: 100%;
            }
        }

        /* Mobile Ready Message Styles - Consolidated in the MOBILE RESPONSIVE STYLES section */

        /*===========================================*/
        /*       SECTION ANIMATION STYLES            */
        /*===========================================*/

        /* Section Containers */
        #person-section, #service-section, #duration-section, #summary-section {
            width: 100%;
            box-sizing: border-box;
        }

        /* Person Section Animation */
        #person-section {
            opacity: 1;
            transform: translateY(0) scale(1);
            transition: opacity 0.7s cubic-bezier(0.25, 0.1, 0.25, 1.0), transform 0.7s cubic-bezier(0.25, 0.1, 0.25, 1.0);
            /* Slightly faster with improved easing for smoother animation */
        }

        /* Service Section Animation */
        #service-section {
            opacity: 1;
            transform: translateY(0) scale(1);
            transition: opacity 0.7s cubic-bezier(0.25, 0.1, 0.25, 1.0), transform 0.7s cubic-bezier(0.25, 0.1, 0.25, 1.0);
            /* Slightly faster with improved easing for smoother animation */
        }

        /* Hidden Section */
        #person-section.hidden-section,
        #service-section.hidden-section {
            opacity: 0;
            transform: translateY(20px) scale(0.95);
            pointer-events: none;
            display: none; /* Completely hide it initially */
        }

        /*===========================================*/
        /*             SUMMARY STYLES                */
        /*===========================================*/

        /* Duration Summary */
        #duration-summary {
            position: relative;
            padding-left: 2rem;
        }

        #duration-summary:before {
            content: '✓';
            position: absolute;
            left: 0.75rem;
            color: hsl(184, 70%, 40%);
            font-weight: bold;
        }

        /* Service Summary */
        #service-summary, #person-summary {
            font-weight: 600;
            font-size: 1.125rem;
            font-family: 'Poppins', sans-serif;
        }

        #service-summary-text:before, #person-summary-text:before {
            content: '✓';
            margin-right: 0.5rem;
            color: hsl(184, 70%, 40%);
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- Main Calculator Card -->
    <main class="calculator-card">
        <!-- Card Header -->
        <header class="card-header">
            <h1 class="text-2xl font-bold" style="font-family: 'Poppins', sans-serif; letter-spacing: -0.045em; text-shadow: 1px 1.01px 1.01px rgba(0,0,0,0.3); color: #fff;">Massage Price Calculator</h1>
            <p class="text-sm mt-1 opacity-90" style="font-family: 'Lato', sans-serif; letter-spacing: 1.3px; color: rgba(255,255,255,0.9);">Build your custom massage experience</p>
        </header>

        <!-- Card Body Content -->
        <div class="card-body">

            <!-- Rive Animation Container with Current Relaxation Level -->
            <section id="animation-container" aria-label="Relaxation level visualization">
                <div id="rive-canvas-container" class="rive-canvas-container centered" aria-hidden="true">
                    <canvas class="rounded-full" id="canvas" width="160" height="160" aria-label="Relaxation animation"></canvas>
                </div>
                <div id="relaxation-text" class="current-relaxation-level hidden-text" role="status" aria-live="polite">
                    <div class="relaxation-title">Relaxation Level</div>
                    <div id="relaxation-level-text" class="relaxation-value">Calm</div>
                </div>
            </section>
            <!-- Load Rive from local installation -->
            <script>
                // Define the path to the WebAssembly files
                window.RIVE_WASM_URL = 'scripts/vendor/rive/rive.wasm';
                window.RIVE_WASM_FALLBACK_URL = 'scripts/vendor/rive/rive_fallback.wasm';
            </script>
            <script src="scripts/vendor/rive/rive.js"></script>
            <script>
                /**
                 * Rive Animation Module
                 * Handles initialization, animation states, and responsive behavior for the Rive animation
                 */
                const RiveAnimationModule = {
                    // DOM element references
                    elements: {
                        canvasContainer: null,
                        relaxationText: null,
                        serviceSection: null,
                        animationContainer: null,
                        relaxationLevelText: null,
                        canvas: null
                    },

                    // Animation state
                    animation: {
                        instance: null,
                        inputs: null,
                        isLoaded: false
                    },

                    // Configuration constants
                    config: {
                        mobileBreakpoint: 640,
                        animationFile: "/assets/images/lotus.riv",
                        stateMachine: "main_state",
                        thresholds: {
                            calm: 30,
                            relaxed: 60,
                            blissful: 90
                        },
                        delays: {
                            introAnimation: 1200,
                            textFadeIn: 200,
                            serviceReveal: {
                                mobile: 650,
                                desktop: 800
                            },
                            positionTransition: 30
                        }
                    },

                    /**
                     * Initialize the Rive animation module
                     */
                    init: function() {
                        this.cacheElements();
                        this.setupInitialState();
                        this.loadAnimation();
                    },

                    /**
                     * Cache DOM element references
                     */
                    cacheElements: function() {
                        this.elements.canvasContainer = document.getElementById('rive-canvas-container');
                        this.elements.relaxationText = document.getElementById('relaxation-text');
                        this.elements.serviceSection = document.getElementById('service-section');
                        this.elements.animationContainer = document.getElementById('animation-container');
                        this.elements.relaxationLevelText = document.getElementById('relaxation-level-text');
                        this.elements.canvas = document.getElementById('canvas');
                    },

                    /**
                     * Set up initial animation state
                     */
                    setupInitialState: function() {
                        // Start with the text hidden and Rive centered
                        this.elements.canvasContainer.classList.add('centered');
                        this.elements.relaxationText.classList.add('hidden-text');

                        // Hide the person and service sections initially for intro animation
                        document.getElementById('person-section').classList.add('hidden-section');
                        this.elements.serviceSection.classList.add('hidden-section');

                        // Check if we're on mobile
                        const isMobile = this.isMobileDevice();

                        // Set appropriate initial styles based on device
                        if (isMobile) {
                            this.applyMobileLayout();
                        }

                        // Add a class for intro animation
                        this.elements.animationContainer.classList.add('intro-animation-active');
                    },

                    /**
                     * Check if the current device is mobile based on screen width
                     * @returns {boolean} True if the device is mobile
                     */
                    isMobileDevice: function() {
                        return window.innerWidth < this.config.mobileBreakpoint;
                    },

                    /**
                     * Apply mobile-specific layout styles
                     */
                    applyMobileLayout: function() {
                        // For mobile, ensure the container is ready for column layout
                        this.elements.animationContainer.style.flexDirection = 'column';

                        // Ensure proper positioning for mobile
                        this.elements.canvasContainer.style.position = 'relative';
                        this.elements.canvasContainer.style.left = 'auto';
                        this.elements.canvasContainer.style.transform = 'none';
                    },

                    /**
                     * Load the Rive animation
                     */
                    loadAnimation: function() {
                        if (typeof rive === 'undefined') {
                            this.handleAnimationError('Rive library not loaded properly');
                            return;
                        }

                        try {
                            const riveInstance = new rive.Rive({
                                src: this.config.animationFile,
                                canvas: this.elements.canvas,
                                autoplay: true,
                                stateMachines: this.config.stateMachine,
                                onLoad: () => this.handleAnimationLoaded(riveInstance),
                                onError: (err) => this.handleAnimationError(err)
                            });
                        } catch (error) {
                            this.handleAnimationError(error);
                        }
                    },

                    /**
                     * Handle successful animation loading
                     * @param {Object} riveInstance - The Rive animation instance
                     */
                    handleAnimationLoaded: function(riveInstance) {
                        riveInstance.resizeDrawingSurfaceToCanvas();

                        // Store animation inputs for state changes
                        const inputs = riveInstance.stateMachineInputs(this.config.stateMachine);

                        this.animation.instance = riveInstance;
                        this.animation.inputs = {
                            calm: inputs.find(i => i.name === 'b_calm'),
                            relaxed: inputs.find(i => i.name === 'b_relaxed'),
                            blissful: inputs.find(i => i.name === 'b_blissful'),
                            zen: inputs.find(i => i.name === 'b_zen')
                        };

                        this.animation.isLoaded = true;

                        // Start with a lower relaxation level
                        this.updateRelaxationState(0);

                        // Begin intro animation sequence
                        this.startIntroAnimation();
                    },

                    /**
                     * Handle animation loading errors
                     * @param {Error} error - The error that occurred
                     */
                    handleAnimationError: function(error) {
                        console.error('Error with Rive animation:', error);

                        // Show a fallback message in the animation container
                        const fallbackMessage = document.createElement('div');
                        fallbackMessage.className = 'animation-error';
                        fallbackMessage.textContent = 'Animation could not be loaded';
                        fallbackMessage.style.color = '#555';
                        fallbackMessage.style.textAlign = 'center';
                        fallbackMessage.style.padding = '2rem';
                        fallbackMessage.style.fontFamily = "'Poppins', sans-serif";

                        // Replace canvas with error message
                        if (this.elements.canvasContainer) {
                            this.elements.canvasContainer.innerHTML = '';
                            this.elements.canvasContainer.appendChild(fallbackMessage);
                        }

                        // Continue with the UI flow despite the animation error
                        this.elements.serviceSection.classList.remove('hidden-section');
                    },

                    /**
                     * Start the intro animation sequence
                     */
                    startIntroAnimation: function() {
                        setTimeout(() => {
                            const isMobile = this.isMobileDevice();

                            if (isMobile) {
                                this.playMobileIntroAnimation();
                            } else {
                                this.playDesktopIntroAnimation();
                            }

                            // Set up responsive behavior
                            this.setupResponsiveHandlers();
                        }, this.config.delays.introAnimation);
                    },

                    /**
                     * Play the mobile version of the intro animation
                     */
                    playMobileIntroAnimation: function() {
                        // For mobile, ensure proper positioning first
                        this.applyMobileLayout();

                        // Remove centered class
                        this.elements.canvasContainer.classList.remove('centered');

                        // Show the text with a fade-in
                        setTimeout(() => {
                            // First make the text container visible but still transparent
                            this.elements.relaxationText.style.display = 'flex';

                            // For mobile, ensure text is properly centered
                            this.elements.relaxationText.style.alignItems = 'center';
                            this.elements.relaxationText.style.textAlign = 'center';

                            // Force a reflow to ensure the display change takes effect
                            void this.elements.relaxationText.offsetHeight;

                            // Then remove the hidden class to trigger the fade-in
                            this.elements.relaxationText.classList.remove('hidden-text');
                            this.elements.animationContainer.classList.remove('intro-animation-active');

                            // After relaxation meter animation completes, animate in the service section
                            setTimeout(() => {
                                this.revealServiceSection();
                            }, this.config.delays.serviceReveal.mobile);
                        }, this.config.delays.textFadeIn);
                    },

                    /**
                     * Play the desktop version of the intro animation
                     */
                    playDesktopIntroAnimation: function() {
                        // For desktop, do the full animation
                        // First ensure it's properly positioned in the center
                        this.elements.canvasContainer.style.position = 'absolute';
                        this.elements.canvasContainer.style.left = '50%';
                        this.elements.canvasContainer.style.transform = 'translateX(-50%)';

                        // Force a reflow to ensure the browser recognizes the centered position
                        void this.elements.canvasContainer.offsetWidth;

                        // Then transition to the final position
                        setTimeout(() => {
                            // Remove the centered class
                            this.elements.canvasContainer.classList.remove('centered');

                            // Reset all positioning to match the original layout
                            this.elements.canvasContainer.style.position = 'relative';
                            this.elements.canvasContainer.style.left = '0px';
                            this.elements.canvasContainer.style.transform = 'translateX(0%)';

                            // After a small additional delay, show the text
                            setTimeout(() => {
                                // First make the text container visible but still transparent
                                this.elements.relaxationText.style.display = 'flex';

                                // For mobile, ensure text is properly centered
                                if (this.isMobileDevice()) {
                                    this.elements.relaxationText.style.alignItems = 'center';
                                    this.elements.relaxationText.style.textAlign = 'center';
                                }

                                // Force a reflow to ensure the display change takes effect
                                void this.elements.relaxationText.offsetHeight;

                                // Then remove the hidden class to trigger the fade-in
                                this.elements.relaxationText.classList.remove('hidden-text');

                                // Remove the intro animation class to restore responsive behavior
                                this.elements.animationContainer.classList.remove('intro-animation-active');

                                // After relaxation meter animation completes, animate in the service section
                                setTimeout(() => {
                                    this.revealServiceSection();
                                }, this.config.delays.serviceReveal.desktop);
                            }, this.config.delays.textFadeIn);
                        }, this.config.delays.positionTransition);
                    },

                    /**
                     * Reveal the person section with animation
                     */
                    revealServiceSection: function() {
                        // First make it visible but still transparent
                        document.getElementById('person-section').style.display = 'block';

                        // Force a reflow before removing the class to ensure the animation works
                        void document.getElementById('person-section').offsetHeight;

                        // Then remove the hidden class to trigger the fade-in animation
                        document.getElementById('person-section').classList.remove('hidden-section');
                    },

                    /**
                     * Set up event handlers for responsive behavior
                     */
                    setupResponsiveHandlers: function() {
                        // Add window resize event listener to ensure responsive behavior
                        window.addEventListener('resize', () => {
                            // Check if we're on mobile
                            const isMobile = this.isMobileDevice();

                            // Only adjust if the intro animation is complete
                            if (!this.elements.animationContainer.classList.contains('intro-animation-active')) {
                                // Reset any inline styles that might interfere with responsiveness
                                this.elements.canvasContainer.style.position = 'relative';
                                this.elements.canvasContainer.style.left = '';
                                this.elements.canvasContainer.style.transform = '';

                                // For mobile, ensure column layout
                                if (isMobile) {
                                    this.elements.animationContainer.style.flexDirection = 'column';
                                } else {
                                    this.elements.animationContainer.style.flexDirection = 'row';
                                }
                            }
                        });

                        // Add window load event to handle page refresh properly
                        window.addEventListener('load', () => {
                            // Check if we're on mobile
                            const isMobile = this.isMobileDevice();

                            // If animation is already complete (page refresh case)
                            if (!this.elements.animationContainer.classList.contains('intro-animation-active') &&
                                !this.elements.relaxationText.classList.contains('hidden-text')) {

                                // For mobile, ensure proper layout
                                if (isMobile) {
                                    // Force column layout
                                    this.elements.animationContainer.style.flexDirection = 'column';

                                    // Ensure proper positioning
                                    this.elements.canvasContainer.style.position = 'relative';
                                    this.elements.canvasContainer.style.left = 'auto';
                                    this.elements.canvasContainer.style.transform = 'none';

                                    // Make sure text is visible
                                    this.elements.relaxationText.style.display = 'flex';

                                    // Ensure text is properly centered on mobile
                                    this.elements.relaxationText.style.alignItems = 'center';
                                    this.elements.relaxationText.style.textAlign = 'center';
                                }
                            }
                        });
                    },

                    /**
                     * Update the relaxation animation state based on score
                     * @param {number} score - The relaxation score (0-100)
                     */
                    updateRelaxationState: function(score) {
                        if (!this.animation.isLoaded || !this.animation.inputs) return;

                        // Reset all states
                        if (this.animation.inputs.calm) this.animation.inputs.calm.value = false;
                        if (this.animation.inputs.relaxed) this.animation.inputs.relaxed.value = false;
                        if (this.animation.inputs.blissful) this.animation.inputs.blissful.value = false;
                        if (this.animation.inputs.zen) this.animation.inputs.zen.value = false;

                        // Set appropriate state based on score and update text
                        if (score > 0) {
                            if (score < this.config.thresholds.calm) {
                                if (this.animation.inputs.calm) this.animation.inputs.calm.value = true;
                                this.elements.relaxationLevelText.textContent = 'Calm';
                            } else if (score < this.config.thresholds.relaxed) {
                                if (this.animation.inputs.relaxed) this.animation.inputs.relaxed.value = true;
                                this.elements.relaxationLevelText.textContent = 'Relaxed';
                            } else if (score < this.config.thresholds.blissful) {
                                if (this.animation.inputs.blissful) this.animation.inputs.blissful.value = true;
                                this.elements.relaxationLevelText.textContent = 'Blissful';
                            } else {
                                if (this.animation.inputs.zen) this.animation.inputs.zen.value = true;
                                this.elements.relaxationLevelText.textContent = 'Zen';
                            }
                        } else {
                            this.elements.relaxationLevelText.textContent = 'Calm';
                        }
                    }
                };

                // Initialize the Rive animation when the DOM is loaded
                document.addEventListener('DOMContentLoaded', () => {
                    RiveAnimationModule.init();
                });

                // Expose the updateRelaxationAnimation function for external use
                function updateRelaxationAnimation(score) {
                    RiveAnimationModule.updateRelaxationState(score);
                }
            </script>
            <!-- End of Rive Animation Container -->

            <!-- Person Selection Section -->
            <section class="mb-6" id="person-section" aria-labelledby="person-section-title">
                <div class="flex justify-between items-center mb-2" id="person-header">
                    <h2 id="person-section-title" class="text-lg font-semibold" style="color: hsl(184, 70%, 35%); font-family: 'Poppins', sans-serif;">Choose Number of People</h2>
                    <button id="person-next-button" class="text-white font-medium py-1 px-4 rounded-md duration-300 shadow-md hidden" style="height: 36px;  border: none; background: linear-gradient(to right, hsl(184, 70%, 35%), hsl(184, 70%, 45%)); font-family: 'Lato', sans-serif;" aria-label="Continue to massage style selection">
                        Next <i class="fas fa-arrow-right ml-1" aria-hidden="true"></i>
                    </button>
                </div>

                <!-- Person summary that will replace the header -->
                <div id="person-summary" class="hidden p-3 rounded-lg mb-2 font-medium shadow-sm flex justify-between items-center" style="background-color: rgba(79, 209, 197, 0.1); color: hsl(184, 70%, 35%); font-family: 'Lato', sans-serif;" role="region" aria-live="polite">
                    <span id="person-summary-text"></span>
                    <button id="edit-person-button" class="text-sm font-medium px-2 py-1 rounded-md transition-colors edit-button" style="height: 32px; border: solid 1px; color: hsl(184, 70%, 40%); font-family: 'Lato', sans-serif;" aria-label="Edit number of people selection">
                        <i class="fas fa-edit mr-1" aria-hidden="true"></i> Edit
                    </button>
                </div>

                <!-- Person options grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3" id="person-options" role="radiogroup" aria-labelledby="person-section-title">
                    <!-- Person options will be populated by JavaScript -->
                </div>
            </section>

            <!-- Service Selection Section -->
            <section class="mb-6 hidden" id="service-section" aria-labelledby="service-section-title">
                <div class="flex justify-between items-center mb-2" id="service-header">
                    <h2 id="service-section-title" class="text-lg font-semibold" style="color: hsl(184, 70%, 35%); font-family: 'Poppins', sans-serif;">Choose Your Massage Style</h2>
                    <button id="service-next-button" class="text-white font-medium py-1 px-4 rounded-md duration-300 shadow-md hidden" style="height: 36px;  border: none; background: linear-gradient(to right, hsl(184, 70%, 35%), hsl(184, 70%, 45%)); font-family: 'Lato', sans-serif;" aria-label="Continue to duration selection">
                        Next <i class="fas fa-arrow-right ml-1" aria-hidden="true"></i>
                    </button>
                </div>

                <!-- Service summary that will replace the header -->
                <div id="service-summary" class="hidden p-3 rounded-lg mb-2 font-medium shadow-sm flex justify-between items-center" style="background-color: rgba(79, 209, 197, 0.1); color: hsl(184, 70%, 35%); font-family: 'Lato', sans-serif;" role="region" aria-live="polite">
                    <span id="service-summary-text"></span>
                    <button id="edit-service-button" class="text-sm font-medium px-2 py-1 rounded-md transition-colors edit-button" style="height: 32px; border: solid 1px; color: hsl(184, 70%, 40%); font-family: 'Lato', sans-serif;" aria-label="Edit massage style selection">
                        <i class="fas fa-edit mr-1" aria-hidden="true"></i> Edit
                    </button>
                </div>

                <!-- Service options grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3" id="service-options" role="radiogroup" aria-labelledby="service-section-title">
                    <!-- Service options will be populated by JavaScript -->
                </div>
            </section>

            <!-- Duration Selection Section -->
            <section class="mb-6 hidden" id="duration-section" aria-labelledby="duration-section-title">
                <div class="flex justify-between items-center mb-3">
                    <h2 id="duration-section-title" class="text-lg font-semibold" style="color: hsl(184, 70%, 35%); font-family: 'Poppins', sans-serif;">Choose Your Duration</h2>
                    <button id="edit-duration-button" class="text-sm font-medium hidden px-2 py-1 rounded-md transition-colors edit-button" style="color: hsl(184, 70%, 40%); font-family: 'Lato', sans-serif;" aria-label="Edit duration selection">
                        <i class="fas fa-edit mr-1" aria-hidden="true"></i> Edit
                    </button>
                </div>

                <!-- Duration options container -->
                <div class="flex flex-wrap gap-2" id="duration-options" role="radiogroup" aria-labelledby="duration-section-title">
                    <!-- Duration options will be populated by JavaScript -->
                </div>

                <!-- Duration summary display -->
                <div id="duration-summary" class="hidden p-3 rounded-lg mt-3 font-medium shadow-sm" style="background-color: rgba(79, 209, 197, 0.1); color: hsl(184, 70%, 35%); font-family: 'Lato', sans-serif;" role="region" aria-live="polite"></div>
            </section>

            <!-- Book Now Button Section -->
            <section id="book-now-container" class="hidden text-center" aria-labelledby="booking-ready-title">
                <div id="booking-ready-title" class="mb-3 text-xl" style="color: #444; font-family: 'Poppins', sans-serif; font-weight: 600; line-height: 1.4; text-shadow: 0 1px 1px rgba(255,255,255,0.8);" role="status" aria-live="polite">
                    Ready for your relaxation journey!
                </div>
                <button id="book-button" class="book-now-button text-white font-bold py-3 px-8 rounded-xl transition duration-300 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed" style="border: none; background: linear-gradient(to right, hsl(184, 70%, 35%), hsl(184, 70%, 45%)); font-family: 'Poppins', sans-serif; text-transform: uppercase; letter-spacing: 2px; font-size: 1.25rem;" disabled aria-label="Complete booking">
                    <i class="fas fa-calendar-check mr-2" aria-hidden="true"></i> Book Now
                </button>
            </section>
        </div>
    </main>

    <!-- Main Application JavaScript -->
    <script>
        /**
         * Massage Booking Application
         * A modular application for booking massage services
         */
        const MassageBooking = {
            /**
             * Data Module - Contains all application data
             */
            Data: {
                /**
                 * Person data - Number of people options
                 * @type {Array<Object>}
                 */
                persons: [
                    {
                        id: 'individual',
                        name: 'Individual',
                        description: 'One person massage session',
                        icon: 'fa-solid fa-user',
                        count: 1
                    },
                    {
                        id: 'couples',
                        name: 'Couples',
                        description: 'Back-to-back individual sessions for two',
                        icon: 'fa-solid fa-user-group',
                        count: 2
                    },
                    {
                        id: 'corporate',
                        name: 'Corporate',
                        description: 'Group sessions for three or more people',
                        icon: 'fa-solid fa-users',
                        count: 3
                    }
                ],

                /**
                 * Service data - Massage styles with their properties
                 * @type {Array<Object>}
                 */
                services: [
                    {
                        id: 'swedish',
                        name: 'Swedish Massage',
                        description: 'Classic, calming, and great for overall relaxation',
                        icon: 'fa-solid fa-spa',
                        relaxationScore: 9
                    },
                    {
                        id: 'deepTissue',
                        name: 'Deep Tissue Massage',
                        description: 'Targets deep muscle layers to release tension',
                        icon: 'fa-solid fa-dumbbell',
                        relaxationScore: 3
                    },
                    {
                        id: 'sports',
                        name: 'Sports Massage',
                        description: 'Athletic recovery and performance enhancement',
                        icon: 'fa-solid fa-person-running',
                        relaxationScore: 4
                    },
                    {
                        id: 'neuromuscular',
                        name: 'Neuromuscular Therapy',
                        description: 'Focused work on trigger points to help relieve pain',
                        icon: 'fa-solid fa-brain',
                        relaxationScore: 5
                    },
                    {
                        id: 'chair',
                        name: 'Chair Massage',
                        description: 'Quick relief for neck, back, and shoulders—fully clothed.',
                        icon: 'fa-solid fa-chair',
                        relaxationScore: 2,
                        hasCustomDurations: true
                    }
                ],

                /**
                 * Duration data - Time options with prices and relaxation scores
                 */
                durations: {
                    /**
                     * Standard durations for most massage types
                     * @type {Array<Object>}
                     */
                    standard: [
                        { time: 60,  price: 100, relaxationScore: 3 },
                        { time: 90,  price: 135, relaxationScore: 6 },
                        { time: 120, price: 200, relaxationScore: 9 }
                    ],

                    /**
                     * Durations specific to couples massage
                     * @type {Array<Object>}
                     */
                    couples: [
                        { time: 60, price: 170, relaxationScore: 4 },
                        { time: 90, price: 240, relaxationScore: 7 }
                    ],

                    /**
                     * Durations specific to chair massage
                     * @type {Array<Object>}
                     */
                    chair: [
                        { time: 60,  price: 125, relaxationScore: 1 },
                        { time: 90,  price: 150, relaxationScore: 2 },
                        { time: 120, price: 225, relaxationScore: 4 }
                    ]
                },

                /**
                 * Get durations for a specific service type and person type
                 * @param {string} serviceId - The ID of the service
                 * @param {string} personId - The ID of the person type
                 * @returns {Array<Object>} Array of duration options
                 */
                getDurationsForService: function(serviceId, personId) {
                    if (personId === 'couples') {
                        return this.durations.couples;
                    } else if (serviceId === 'chair') {
                        return this.durations.chair;
                    } else {
                        return this.durations.standard;
                    }
                }
            },

            /**
             * State Module - Manages application state
             */
            State: {
                /**
                 * Current selected person type
                 * @type {Object|null}
                 */
                selectedPerson: null,

                /**
                 * Current selected service
                 * @type {Object|null}
                 */
                selectedService: null,

                /**
                 * Current selected duration
                 * @type {Object|null}
                 */
                selectedDuration: null,

                /**
                 * Maximum possible relaxation score
                 * @type {number}
                 */
                maxRelaxationScore: 1,

                /**
                 * Whether the person section is collapsed
                 * @type {boolean}
                 */
                personCollapsed: false,

                /**
                 * Whether the service section is collapsed
                 * @type {boolean}
                 */
                serviceCollapsed: false,

                /**
                 * Whether the duration section is collapsed
                 * @type {boolean}
                 */
                durationCollapsed: false,

                /**
                 * Reset the state to initial values
                 */
                reset: function() {
                    this.selectedPerson = null;
                    this.selectedService = null;
                    this.selectedDuration = null;
                    this.maxRelaxationScore = 1;
                    this.personCollapsed = false;
                    this.serviceCollapsed = false;
                    this.durationCollapsed = false;
                }
            },

            /**
             * DOM Module - Manages DOM references and manipulations
             */
            DOM: {
                /**
                 * References to DOM elements
                 */
                elements: {
                    // Main containers
                    personOptionsContainer: null,
                    serviceOptionsContainer: null,
                    durationOptionsContainer: null,
                    bookNowContainer: null,

                    // Section containers
                    personSection: null,
                    serviceSection: null,
                    durationSection: null,
                    personSummary: null,
                    serviceSummary: null,
                    durationSummary: null,

                    // Interactive elements
                    relaxationLevelText: null,
                    bookButton: null,
                    editPersonButton: null,
                    editServiceButton: null,
                    editDurationButton: null,
                    personNextButton: null,
                    serviceNextButton: null
                },

                /**
                 * Cache all DOM element references
                 */
                cacheElements: function() {
                    // Main containers
                    this.elements.personOptionsContainer = document.getElementById('person-options');
                    this.elements.serviceOptionsContainer = document.getElementById('service-options');
                    this.elements.durationOptionsContainer = document.getElementById('duration-options');
                    this.elements.bookNowContainer = document.getElementById('book-now-container');

                    // Section containers
                    this.elements.personSection = document.getElementById('person-section');
                    this.elements.serviceSection = document.getElementById('service-section');
                    this.elements.durationSection = document.getElementById('duration-section');
                    this.elements.personSummary = document.getElementById('person-summary');
                    this.elements.serviceSummary = document.getElementById('service-summary');
                    this.elements.durationSummary = document.getElementById('duration-summary');

                    // Interactive elements
                    this.elements.relaxationLevelText = document.getElementById('relaxation-level-text');
                    this.elements.bookButton = document.getElementById('book-button');
                    this.elements.editPersonButton = document.getElementById('edit-person-button');
                    this.elements.editServiceButton = document.getElementById('edit-service-button');
                    this.elements.editDurationButton = document.getElementById('edit-duration-button');
                    this.elements.personNextButton = document.getElementById('person-next-button');
                    this.elements.serviceNextButton = document.getElementById('service-next-button');
                },

                /**
                 * Toggle element visibility
                 * @param {HTMLElement} element - The element to toggle
                 * @param {boolean} show - Whether to show or hide the element
                 */
                toggleVisibility: function(element, show) {
                    if (show) {
                        element.classList.remove('hidden');
                    } else {
                        element.classList.add('hidden');
                    }
                },

                /**
                 * Toggle a class on an element
                 * @param {HTMLElement} element - The element to modify
                 * @param {string} className - The class to toggle
                 * @param {boolean} add - Whether to add or remove the class
                 */
                toggleClass: function(element, className, add) {
                    if (add) {
                        element.classList.add(className);
                    } else {
                        element.classList.remove(className);
                    }
                }
            },

            /**
             * Calculator Module - Handles price and relaxation calculations
             */
            Calculator: {
                /**
                 * Calculate price for a duration based on selected service
                 * @param {Object} duration - The duration object
                 * @returns {number} The calculated price
                 */
                calculatePriceForDuration: function(duration) {
                    const state = MassageBooking.State;

                    if (!state.selectedService) return duration.price;

                    let price = duration.price;
                    if (state.selectedService.priceAdjustment &&
                        state.selectedService.id !== 'couples' &&
                        state.selectedService.id !== 'chair') {
                        price += state.selectedService.priceAdjustment;
                    }

                    return price;
                },

                /**
                 * Calculate total price for the selected service and duration
                 * @returns {number} The total price
                 */
                calculateTotalPrice: function() {
                    const state = MassageBooking.State;

                    if (!state.selectedService || !state.selectedDuration) return 0;

                    return this.calculatePriceForDuration(state.selectedDuration);
                },

                /**
                 * Calculate relaxation score based on selected service and duration
                 * @returns {number} The relaxation score
                 */
                calculateRelaxationScore: function() {
                    const state = MassageBooking.State;

                    if (!state.selectedService) return 0;

                    let score = state.selectedService.relaxationScore;

                    if (state.selectedDuration) {
                        score += state.selectedDuration.relaxationScore;
                    }

                    return score;
                },

                /**
                 * Calculate maximum possible relaxation score for the selected service
                 */
                recalculateMaxRelaxationScore: function() {
                    const state = MassageBooking.State;

                    if (!state.selectedService || !state.selectedPerson) {
                        state.maxRelaxationScore = 1;
                        return;
                    }

                    // Get appropriate durations for the selected service and person type
                    const durations = MassageBooking.Data.getDurationsForService(state.selectedService.id, state.selectedPerson.id);

                    // Find the maximum relaxation score from available durations
                    const maxDurationScore = Math.max(...durations.map(d => d.relaxationScore));

                    // Update the maximum relaxation score
                    state.maxRelaxationScore = Math.max(1, state.selectedService.relaxationScore + maxDurationScore);
                }
            },

            /**
             * UI Module - Manages UI state and interactions
             */
            UI: {
                /**
                 * Initialize the calculator UI
                 */
                init: function() {
                    // Populate person options
                    this.populatePersonOptions();

                    // Populate service options
                    this.populateServices();

                    // Set up event listeners
                    this.setupEventListeners();

                    // Update UI state
                    this.updateUI();
                },

                /**
                 * Set up event listeners for UI controls
                 */
                setupEventListeners: function() {
                    const dom = MassageBooking.DOM.elements;

                    // Edit person button
                    dom.editPersonButton.addEventListener('click', () => {
                        this.expandPersonSection();
                        this.hideServiceSection();
                        this.hideDurationSection();
                    });

                    // Edit service button
                    dom.editServiceButton.addEventListener('click', () => {
                        this.expandServiceSection();
                        if (MassageBooking.State.durationCollapsed) {
                            this.collapseDurationSection();
                        } else {
                            this.hideDurationSection();
                        }
                    });

                    // Edit duration button
                    dom.editDurationButton.addEventListener('click', () => {
                        this.expandDurationSection();
                    });

                    // Next button for person section
                    dom.personNextButton.addEventListener('click', () => {
                        this.collapsePersonSection();

                        // Force the service section to be visible
                        const serviceSection = document.getElementById('service-section');
                        serviceSection.classList.remove('hidden-section');
                        serviceSection.style.display = 'block';
                        serviceSection.classList.remove('hidden');

                        // Reset animation for service options
                        document.querySelectorAll('#service-options .service-option').forEach((option, index) => {
                            // Reset the animation by removing and re-adding the element to the DOM
                            option.style.animation = 'none';
                            option.offsetHeight; // Trigger reflow
                            option.style.animation = '';
                            option.style.setProperty('--animation-order', index);
                        });
                    });

                    // Next button for service section
                    dom.serviceNextButton.addEventListener('click', () => {
                        this.collapseServiceSection();
                        this.showDurationSection();
                    });

                    // Book button
                    dom.bookButton.addEventListener('click', () => {
                        this.handleBooking();
                    });
                },

                /**
                 * Collapse the person section and show summary
                 */
                collapsePersonSection: function() {
                    const dom = MassageBooking.DOM.elements;
                    const state = MassageBooking.State;

                    if (state.selectedPerson) {
                        // Hide person options and next button
                        MassageBooking.DOM.toggleVisibility(dom.personOptionsContainer, false);
                        MassageBooking.DOM.toggleVisibility(dom.personNextButton, false);

                        // Hide the person header with the "Choose Number of People" text
                        MassageBooking.DOM.toggleVisibility(document.getElementById('person-header'), false);

                        // Show the person summary in place of the header
                        MassageBooking.DOM.toggleVisibility(dom.personSummary, true);
                        document.getElementById('person-summary-text').textContent = state.selectedPerson.name;

                        // Update state
                        state.personCollapsed = true;

                        // Force the service section to be visible
                        const serviceSection = document.getElementById('service-section');
                        serviceSection.classList.remove('hidden-section');
                        serviceSection.style.display = 'block';
                        serviceSection.classList.remove('hidden');

                        // Reset animation for service options
                        document.querySelectorAll('#service-options .service-option').forEach((option, index) => {
                            // Reset the animation by removing and re-adding the element to the DOM
                            option.style.animation = 'none';
                            option.offsetHeight; // Trigger reflow
                            option.style.animation = '';
                            option.style.setProperty('--animation-order', index);
                        });
                    }
                },

                /**
                 * Expand the person section to show all options
                 */
                expandPersonSection: function() {
                    const dom = MassageBooking.DOM.elements;
                    const state = MassageBooking.State;

                    // Show person options and hide summary
                    MassageBooking.DOM.toggleVisibility(dom.personOptionsContainer, true);
                    MassageBooking.DOM.toggleVisibility(dom.personSummary, false);

                    // Show the person header with the "Choose Number of People" text
                    MassageBooking.DOM.toggleVisibility(document.getElementById('person-header'), true);

                    // Show next button if a person is selected
                    if (state.selectedPerson) {
                        MassageBooking.DOM.toggleVisibility(dom.personNextButton, true);
                    }

                    // Update state
                    state.personCollapsed = false;

                    // Repopulate service options based on the current person selection
                    this.populateServices();

                    // Reset animation for person options
                    document.querySelectorAll('#person-options .service-option').forEach((option, index) => {
                        // Reset the animation by removing and re-adding the element to the DOM
                        option.style.animation = 'none';
                        option.offsetHeight; // Trigger reflow
                        option.style.animation = '';
                        option.style.setProperty('--animation-order', index);
                    });
                },

                /**
                 * Show the service section
                 */
                showServiceSection: function() {
                    const dom = MassageBooking.DOM.elements;
                    // Make sure the service section is visible
                    dom.serviceSection.classList.remove('hidden-section');
                    dom.serviceSection.style.display = 'block';
                    dom.serviceSection.classList.remove('hidden');
                },

                /**
                 * Hide the service section
                 */
                hideServiceSection: function() {
                    const dom = MassageBooking.DOM.elements;
                    MassageBooking.DOM.toggleVisibility(dom.serviceSection, false);
                },

                /**
                 * Collapse the service section and show summary
                 */
                collapseServiceSection: function() {
                    const dom = MassageBooking.DOM.elements;
                    const state = MassageBooking.State;

                    if (state.selectedService) {
                        // Hide service options and next button
                        MassageBooking.DOM.toggleVisibility(dom.serviceOptionsContainer, false);
                        MassageBooking.DOM.toggleVisibility(dom.serviceNextButton, false);

                        // Hide the service header with the "Choose Your Massage Style" text
                        MassageBooking.DOM.toggleVisibility(document.getElementById('service-header'), false);

                        // Show the service summary in place of the header
                        MassageBooking.DOM.toggleVisibility(dom.serviceSummary, true);
                        document.getElementById('service-summary-text').textContent = state.selectedService.name;

                        // Update state
                        state.serviceCollapsed = true;
                    }
                },

                /**
                 * Expand the service section to show all options
                 */
                expandServiceSection: function() {
                    const dom = MassageBooking.DOM.elements;
                    const state = MassageBooking.State;

                    // Show service options and hide summary
                    MassageBooking.DOM.toggleVisibility(dom.serviceOptionsContainer, true);
                    MassageBooking.DOM.toggleVisibility(dom.serviceSummary, false);

                    // Show the service header with the "Choose Your Massage Style" text
                    MassageBooking.DOM.toggleVisibility(document.getElementById('service-header'), true);

                    // Show next button if a service is selected
                    if (state.selectedService) {
                        MassageBooking.DOM.toggleVisibility(dom.serviceNextButton, true);
                    }

                    // Update state
                    state.serviceCollapsed = false;

                    // Reset animation for service options
                    document.querySelectorAll('.service-option').forEach((option, index) => {
                        // Reset the animation by removing and re-adding the element to the DOM
                        option.style.animation = 'none';
                        option.offsetHeight; // Trigger reflow
                        option.style.animation = '';
                        option.style.setProperty('--animation-order', index);
                    });
                },

                /**
                 * Show the duration section
                 */
                showDurationSection: function() {
                    const dom = MassageBooking.DOM.elements;
                    MassageBooking.DOM.toggleVisibility(dom.durationSection, true);
                },

                /**
                 * Hide the duration section
                 */
                hideDurationSection: function() {
                    const dom = MassageBooking.DOM.elements;
                    MassageBooking.DOM.toggleVisibility(dom.durationSection, false);
                },

                /**
                 * Collapse the duration section and show summary
                 */
                collapseDurationSection: function() {
                    const dom = MassageBooking.DOM.elements;
                    const state = MassageBooking.State;
                    const calculator = MassageBooking.Calculator;

                    if (state.selectedDuration) {
                        // Hide duration options
                        MassageBooking.DOM.toggleVisibility(dom.durationOptionsContainer, false);

                        // Show duration summary
                        MassageBooking.DOM.toggleVisibility(dom.durationSummary, true);
                        dom.durationSummary.textContent = `${state.selectedDuration.time} minutes - $${calculator.calculatePriceForDuration(state.selectedDuration)}`;

                        // Show edit button
                        MassageBooking.DOM.toggleVisibility(dom.editDurationButton, true);

                        // Update state
                        state.durationCollapsed = true;
                    }
                },

                /**
                 * Expand the duration section to show all options
                 */
                expandDurationSection: function() {
                    const dom = MassageBooking.DOM.elements;
                    const state = MassageBooking.State;

                    // Show duration options
                    MassageBooking.DOM.toggleVisibility(dom.durationOptionsContainer, true);

                    // Hide duration summary and edit button
                    MassageBooking.DOM.toggleVisibility(dom.durationSummary, false);
                    MassageBooking.DOM.toggleVisibility(dom.editDurationButton, false);

                    // Update state
                    state.durationCollapsed = false;
                },

                /**
                 * Populate person options in the UI
                 */
                populatePersonOptions: function() {
                    const dom = MassageBooking.DOM.elements;
                    const persons = MassageBooking.Data.persons;

                    // Clear existing options
                    dom.personOptionsContainer.innerHTML = '';

                    // Create person option elements
                    persons.forEach((person, index) => {
                        const personElement = document.createElement('div');
                        personElement.className = 'service-option';

                        // Set animation order for staggered appearance
                        personElement.style.setProperty('--animation-order', index);

                        // Set inner HTML with person details and accessibility attributes
                        personElement.setAttribute('role', 'radio');
                        personElement.setAttribute('aria-checked', 'false');
                        personElement.setAttribute('tabindex', '0');
                        personElement.setAttribute('aria-label', `${person.name}: ${person.description}`);
                        personElement.innerHTML = `
                            <div class="flex items-center gap-3">
                                <div class="service-selector-icon" aria-hidden="true">
                                    <i class="${person.icon}"></i>
                                </div>
                                <div class="service-text-container">
                                    <h3 class="service-selector-h3 font-semibold">${person.name}</h3>
                                    <p class="service-selector-p text-sm">${person.description}</p>
                                </div>
                            </div>
                        `;

                        // Add click and keyboard event listeners for accessibility
                        const selectPerson = () => {
                            // Update selected state visually
                            document.querySelectorAll('#person-options .service-option').forEach(el => {
                                MassageBooking.DOM.toggleClass(el, 'selected', false);
                                el.setAttribute('aria-checked', 'false');
                                el.tabIndex = 0;
                            });
                            MassageBooking.DOM.toggleClass(personElement, 'selected', true);
                            personElement.setAttribute('aria-checked', 'true');
                            personElement.tabIndex = -1; // Remove from tab order once selected

                            // Update application state
                            MassageBooking.State.selectedPerson = person;
                            MassageBooking.State.selectedService = null;
                            MassageBooking.State.selectedDuration = null;

                            // Show the Next button
                            MassageBooking.DOM.toggleVisibility(dom.personNextButton, true);

                            // Repopulate service options based on the new person selection
                            this.populateServices();

                            // Update UI
                            this.updateUI();
                        };

                        // Mouse click event
                        personElement.addEventListener('click', selectPerson);

                        // Keyboard event for accessibility
                        personElement.addEventListener('keydown', (e) => {
                            // Select on Space or Enter key
                            if (e.key === ' ' || e.key === 'Enter') {
                                e.preventDefault();
                                selectPerson();
                            }
                        });

                        // Add to container
                        dom.personOptionsContainer.appendChild(personElement);
                    });
                },

                /**
                 * Populate service options in the UI
                 */
                populateServices: function() {
                    const dom = MassageBooking.DOM.elements;
                    const state = MassageBooking.State;
                    const allServices = MassageBooking.Data.services;

                    // Filter services based on person selection
                    // Remove Chair massage for couples
                    let services = allServices;
                    if (state.selectedPerson && state.selectedPerson.id === 'couples') {
                        services = allServices.filter(service => service.id !== 'chair');
                    }

                    // Clear existing options
                    dom.serviceOptionsContainer.innerHTML = '';

                    // Create service option elements
                    services.forEach((service, index) => {
                        const serviceElement = document.createElement('div');
                        serviceElement.className = 'service-option';

                        // Set animation order for staggered appearance
                        serviceElement.style.setProperty('--animation-order', index);

                        // Set inner HTML with service details and accessibility attributes
                        serviceElement.setAttribute('role', 'radio');
                        serviceElement.setAttribute('aria-checked', 'false');
                        serviceElement.setAttribute('tabindex', '0');
                        serviceElement.setAttribute('aria-label', `${service.name}: ${service.description}`);
                        serviceElement.innerHTML = `
                            <div class="flex items-center gap-3">
                                <div class="service-selector-icon" aria-hidden="true">
                                    <i class="${service.icon}"></i>
                                </div>
                                <div class="service-text-container">
                                    <h3 class="service-selector-h3 font-semibold">${service.name}</h3>
                                    <p class="service-selector-p text-sm">${service.description}</p>
                                </div>
                            </div>
                        `;

                        // Add click and keyboard event listeners for accessibility
                        const selectService = () => {
                            // Update selected state visually
                            document.querySelectorAll('#service-options .service-option').forEach(el => {
                                MassageBooking.DOM.toggleClass(el, 'selected', false);
                                el.setAttribute('aria-checked', 'false');
                                el.tabIndex = 0;
                            });
                            MassageBooking.DOM.toggleClass(serviceElement, 'selected', true);
                            serviceElement.setAttribute('aria-checked', 'true');
                            serviceElement.tabIndex = -1; // Remove from tab order once selected

                            // Update application state
                            MassageBooking.State.selectedService = service;
                            MassageBooking.State.selectedDuration = null;

                            // Show the Next button
                            MassageBooking.DOM.toggleVisibility(dom.serviceNextButton, true);

                            // Update durations and UI
                            this.populateDurations();
                            MassageBooking.Calculator.recalculateMaxRelaxationScore();
                            this.updateUI();
                        };

                        // Mouse click event
                        serviceElement.addEventListener('click', selectService);

                        // Keyboard event for accessibility
                        serviceElement.addEventListener('keydown', (e) => {
                            // Select on Space or Enter key
                            if (e.key === ' ' || e.key === 'Enter') {
                                e.preventDefault();
                                selectService();
                            }
                        });

                        // Add to container
                        dom.serviceOptionsContainer.appendChild(serviceElement);
                    });
                },

                /**
                 * Populate duration options based on selected service and person type
                 */
                populateDurations: function() {
                    const dom = MassageBooking.DOM.elements;
                    const state = MassageBooking.State;
                    const calculator = MassageBooking.Calculator;

                    // Clear existing options
                    dom.durationOptionsContainer.innerHTML = '';

                    // Exit if no service or person is selected
                    if (!state.selectedService || !state.selectedPerson) return;

                    // Get appropriate durations for the selected service and person type
                    const durations = MassageBooking.Data.getDurationsForService(state.selectedService.id, state.selectedPerson.id);

                    // Create duration option elements
                    durations.forEach(duration => {
                        const durationElement = document.createElement('div');
                        durationElement.className = 'duration-option text-center';

                        // Set inner HTML with duration details and accessibility attributes
                        durationElement.setAttribute('role', 'radio');
                        durationElement.setAttribute('aria-checked', 'false');
                        durationElement.setAttribute('tabindex', '0');
                        durationElement.setAttribute('aria-label', `${duration.time} minutes for $${calculator.calculatePriceForDuration(duration)}`);
                        durationElement.innerHTML = `
                            <div class="font-semibold duration-selector-h3" style="font-family: 'Poppins', sans-serif;">${duration.time} min</div>
                            <div class="text-sm duration-selector-p" style="font-family: 'Poppins', sans-serif; font-size: 0.85rem; margin-top: 0.1rem; line-height: 1.2rem; ">$${calculator.calculatePriceForDuration(duration)}</div>
                        `;

                        // Add click and keyboard event listeners for accessibility
                        const selectDuration = () => {
                            // Update selected state visually
                            document.querySelectorAll('.duration-option').forEach(el => {
                                MassageBooking.DOM.toggleClass(el, 'selected', false);
                                el.setAttribute('aria-checked', 'false');
                                el.tabIndex = 0;
                            });
                            MassageBooking.DOM.toggleClass(durationElement, 'selected', true);
                            durationElement.setAttribute('aria-checked', 'true');
                            durationElement.tabIndex = -1; // Remove from tab order once selected

                            // Update application state
                            state.selectedDuration = duration;

                            // Update UI
                            this.updateUI();
                        };

                        // Mouse click event
                        durationElement.addEventListener('click', selectDuration);

                        // Keyboard event for accessibility
                        durationElement.addEventListener('keydown', (e) => {
                            // Select on Space or Enter key
                            if (e.key === ' ' || e.key === 'Enter') {
                                e.preventDefault();
                                selectDuration();
                            }
                        });

                        // Add to container
                        dom.durationOptionsContainer.appendChild(durationElement);
                    });
                },

                /**
                 * Update UI based on current selections
                 */
                updateUI: function() {
                    const dom = MassageBooking.DOM.elements;
                    const state = MassageBooking.State;
                    const calculator = MassageBooking.Calculator;

                    // Calculate relaxation score for Rive animation
                    const relaxationScore = calculator.calculateRelaxationScore();
                    const relaxationPercentage = Math.min(100, Math.round((relaxationScore / state.maxRelaxationScore) * 100));

                    // Update Rive animation
                    updateRelaxationAnimation(relaxationPercentage);

                    // Update Person Next button visibility
                    MassageBooking.DOM.toggleVisibility(
                        dom.personNextButton,
                        state.selectedPerson && !state.personCollapsed
                    );

                    // Update Service Next button visibility
                    MassageBooking.DOM.toggleVisibility(
                        dom.serviceNextButton,
                        state.selectedService && !state.serviceCollapsed
                    );

                    // Only show service section if person is collapsed
                    if (state.selectedPerson && state.personCollapsed) {
                        this.showServiceSection();
                    } else if (!state.selectedPerson) {
                        this.hideServiceSection();
                    }

                    // Only show duration section if service is collapsed
                    if (state.selectedService && state.serviceCollapsed) {
                        this.showDurationSection();
                    } else if (!state.selectedService) {
                        this.hideDurationSection();
                    }

                    // Update Book Now button visibility and animation
                    this.updateBookNowSection();
                },

                /**
                 * Update the Book Now section based on current selections
                 */
                updateBookNowSection: function() {
                    const dom = MassageBooking.DOM.elements;
                    const state = MassageBooking.State;
                    const calculator = MassageBooking.Calculator;

                    if (state.selectedPerson && state.selectedService && state.selectedDuration) {
                        // Show the Book Now button when all selections are made
                        MassageBooking.DOM.toggleVisibility(dom.bookNowContainer, true);

                        // Calculate total price
                        const totalPrice = calculator.calculateTotalPrice();

                        // Update the message with selected person, service, duration, and price
                        const readyMessage = dom.bookNowContainer.querySelector('.mb-3.text-xl');

                        // Add a subtle background and padding to the message
                        readyMessage.style.padding = "1rem";
                        readyMessage.style.borderRadius = "0.75rem";
                        readyMessage.style.background = "rgba(255, 255, 255, 0.6)";
                        readyMessage.style.boxShadow = "0 2px 8px rgba(79, 209, 197, 0.15)";
                        readyMessage.style.marginBottom = "1.5rem";

                        // Use stacked format with separators for both mobile and desktop
                        readyMessage.innerHTML = `
                        <span class="ready-message-title" style="display: block; margin-bottom: 0.75rem; font-size: 1.25rem;">Your massage experience is ready!</span>
                        <div class="ready-message-details">
                            <div class="ready-message-item" style="font-size: 1.3rem;">${state.selectedPerson.name}</div>
                            <div class="ready-message-item">${state.selectedService.name}</div>
                            <div class="ready-message-item">${state.selectedDuration.time} minutes</div>
                            <div class="ready-message-item">$${totalPrice}</div>
                        </div>`;

                        // Use setTimeout to trigger the animation after the element is visible
                        setTimeout(() => {
                            dom.bookNowContainer.classList.add('visible');

                            // Add animation to the ready message
                            readyMessage.style.animation = 'highlight-text 3s ease-in-out infinite';
                        }, 50);

                        dom.bookButton.disabled = false;
                    } else {
                        // Hide the Book Now button if either selection is missing
                        dom.bookNowContainer.classList.remove('visible');

                        // Stop the animation on the ready message
                        const readyMessage = dom.bookNowContainer.querySelector('.mb-3.text-xl');
                        if (readyMessage) {
                            readyMessage.style.animation = 'none';
                        }

                        // Use setTimeout to hide the element after the fade-out animation
                        setTimeout(() => {
                            if (!state.selectedService || !state.selectedDuration) {
                                MassageBooking.DOM.toggleVisibility(dom.bookNowContainer, false);
                            }
                        }, 350); // Match the transition duration

                        dom.bookButton.disabled = true;
                    }
                },

                /**
                 * Handle the booking button click
                 */
                handleBooking: function() {
                    const state = MassageBooking.State;
                    const calculator = MassageBooking.Calculator;

                    if (!state.selectedService || !state.selectedDuration) return;

                    const totalPrice = calculator.calculateTotalPrice();

                    // Create a custom styled alert instead of using the default alert
                    this.showBookingConfirmation(state.selectedService, state.selectedDuration, totalPrice);
                },

                /**
                 * Show booking confirmation dialog
                 * @param {Object} service - The selected service
                 * @param {Object} duration - The selected duration
                 * @param {number} totalPrice - The total price
                 */
                showBookingConfirmation: function(service, duration, totalPrice) {
                    // Create overlay with accessibility attributes
                    const alertOverlay = document.createElement('div');
                    alertOverlay.setAttribute('role', 'dialog');
                    alertOverlay.setAttribute('aria-modal', 'true');
                    alertOverlay.setAttribute('aria-labelledby', 'booking-confirmation-title');
                    alertOverlay.setAttribute('aria-describedby', 'booking-confirmation-content');
                    alertOverlay.style.position = 'fixed';
                    alertOverlay.style.top = '0';
                    alertOverlay.style.left = '0';
                    alertOverlay.style.width = '100%';
                    alertOverlay.style.height = '100%';
                    alertOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
                    alertOverlay.style.display = 'flex';
                    alertOverlay.style.justifyContent = 'center';
                    alertOverlay.style.alignItems = 'center';
                    alertOverlay.style.zIndex = '9999';

                    // Create alert box
                    const alertBox = document.createElement('div');
                    alertBox.style.backgroundColor = 'white';
                    alertBox.style.padding = '2rem';
                    alertBox.style.borderRadius = '1rem';
                    alertBox.style.maxWidth = '90%';
                    alertBox.style.width = '400px';
                    alertBox.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.2)';
                    alertBox.style.textAlign = 'center';
                    alertBox.style.position = 'relative';
                    alertBox.style.border = '1px solid rgba(79, 209, 197, 0.3)';

                    // Create title with ID for aria-labelledby
                    const title = document.createElement('h2');
                    title.id = 'booking-confirmation-title';
                    title.textContent = 'Booking Confirmed!';
                    title.style.color = 'hsl(184, 70%, 35%)';
                    title.style.marginBottom = '1rem';
                    title.style.fontFamily = "'Poppins', sans-serif";

                    // Create content with ID for aria-describedby
                    const content = document.createElement('div');
                    content.id = 'booking-confirmation-content';
                    content.style.marginBottom = '1.5rem';
                    content.style.fontFamily = "'Lato', sans-serif";
                    content.style.color = '#555';
                    content.style.lineHeight = '1.6';
                    content.innerHTML = `
                        <p><strong>Service:</strong> ${service.name}</p>
                        <p><strong>Duration:</strong> ${duration.time} minutes</p>
                        <p><strong>Total Price:</strong> $${totalPrice}</p>
                    `;

                    // Create thank you message
                    const thankYou = document.createElement('p');
                    thankYou.textContent = 'Thank you for your booking!';
                    thankYou.style.fontWeight = 'bold';
                    thankYou.style.marginBottom = '1.5rem';
                    thankYou.style.color = '#333';

                    // Create close button with accessibility attributes
                    const closeButton = document.createElement('button');
                    closeButton.textContent = 'Close';
                    closeButton.setAttribute('aria-label', 'Close booking confirmation');
                    closeButton.style.padding = '0.5rem 1.5rem';
                    closeButton.style.background = 'linear-gradient(to right, hsl(184, 70%, 35%), hsl(184, 70%, 45%))';
                    closeButton.style.color = 'white';
                    closeButton.style.border = 'none';
                    closeButton.style.borderRadius = '0.5rem';
                    closeButton.style.cursor = 'pointer';
                    closeButton.style.fontFamily = "'Lato', sans-serif";
                    closeButton.style.fontWeight = 'bold';
                    closeButton.style.textTransform = 'uppercase';
                    closeButton.style.letterSpacing = '1px';
                    closeButton.style.minHeight = '44px'; // Ensure adequate touch target

                    // Add close button event listeners for accessibility
                    closeButton.addEventListener('click', () => {
                        document.body.removeChild(alertOverlay);
                    });

                    // Close on Escape key press
                    const handleEscapeKey = (e) => {
                        if (e.key === 'Escape') {
                            document.body.removeChild(alertOverlay);
                            document.removeEventListener('keydown', handleEscapeKey);
                        }
                    };
                    document.addEventListener('keydown', handleEscapeKey);

                    // Trap focus within the modal for accessibility
                    alertBox.addEventListener('keydown', (e) => {
                        if (e.key === 'Tab') {
                            // There's only one focusable element (the close button)
                            // so just prevent default and keep focus on the button
                            e.preventDefault();
                            closeButton.focus();
                        }
                    });

                    // Assemble the alert
                    alertBox.appendChild(title);
                    alertBox.appendChild(content);
                    alertBox.appendChild(thankYou);
                    alertBox.appendChild(closeButton);
                    alertOverlay.appendChild(alertBox);
                    document.body.appendChild(alertOverlay);

                    // Set focus to the close button when the dialog opens
                    setTimeout(() => closeButton.focus(), 50);
                }
            }
        };

        /**
         * Initialize the entire application
         */
        MassageBooking.init = function() {
            // Cache DOM elements
            this.DOM.cacheElements();

            // Initialize UI
            this.UI.init();
        };

        // Initialize the application when the DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize the application
            MassageBooking.init();
        });
    </script>
</body>
</html>
